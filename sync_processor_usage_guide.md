# 同步消息处理脚本使用指南

## 概述

本脚本实现了云端到末端设备（道闸控制器）的同步消息处理功能，解决了消息顺序不对应的问题。

## 功能特性

1. **消息队列缓存**：接收云端消息并缓存到队列中
2. **同步处理**：一次只处理一条消息，确保顺序执行
3. **ID和时间戳管理**：自动处理消息中的ID和时间戳信息
4. **响应增强**：在设备响应中添加源ID和当前时间戳
5. **超时保护**：防止消息处理卡死
6. **调试支持**：可开启调试模式查看处理状态

## 消息格式

### 云端下发消息格式
```
[原始指令数据] + [消息ID(4字节)] + [时间戳(4字节)]
```

### 设备响应格式
```
[原始响应数据] + [源消息ID(4字节)] + [当前时间戳(4字节)]
```

## 配置参数

脚本中定义了以下可配置的FLASH参数：

- `QUEUE_MAX_SIZE`: 队列最大长度（默认10，可根据MTU性能调整）
- `MSG_TIMEOUT`: 消息处理超时时间（默认5000ms）
- `DEBUG_MODE`: 调试模式开关（1=开启，0=关闭）

## 安装和配置

### 1. 脚本部署
1. 使用IOTService工具连接MTU设备
2. 进入"高级设置" > "编辑脚本"
3. 点击"更新脚本"，选择`sync_message_processor.txt`文件
4. 确认导入后点击"重新启动"

### 2. 参数配置
1. 重启后通过IOTService工具"读取脚本参数"
2. 根据实际需求调整参数：
   - 如果MTU性能较低，可减小`QUEUE_MAX_SIZE`
   - 如果设备响应较慢，可增加`MSG_TIMEOUT`
   - 生产环境建议关闭`DEBUG_MODE`

### 3. 网络配置
确保MTU的MQTT配置正确：
- Socket名称：`netp`
- 串口名称：`uart0`
- 关闭原有的"UART TO MQTT"透传功能

## 工作流程

1. **消息接收**：云端通过MQTT发送带ID和时间戳的指令
2. **队列缓存**：脚本将消息存入队列等待处理
3. **顺序处理**：从队列头部取出消息，解析出原始指令
4. **设备通信**：将原始指令发送给末端设备
5. **响应处理**：接收设备响应，添加源ID和当前时间戳
6. **结果返回**：将增强后的响应发送给云端
7. **继续处理**：处理队列中的下一条消息

## 状态说明

### 处理状态
- `0`: 空闲状态，可以处理新消息
- `1`: 处理中，正在发送指令给设备
- `2`: 等待响应，已发送指令等待设备回复

### 队列状态
- 队列采用环形缓冲区实现
- 支持最多10条消息缓存（可配置）
- 队列满时新消息会被丢弃

## 调试和监控

### 开启调试模式
设置`DEBUG_MODE = 1`后，脚本会通过串口输出调试信息：

```
[DEBUG] Cloud connected
[DEBUG] Message enqueued, queue size: 1
[DEBUG] Command sent to device, waiting for response
[DEBUG] Response sent to cloud with ID and timestamp
Status - Queue: 0, State: 0, Connected: 1
```

### 状态监控
每10秒输出一次状态信息，包括：
- 队列长度
- 处理状态
- 连接状态

## 错误处理

### 消息超时
- 如果设备在5秒内未响应，自动重置处理状态
- 继续处理队列中的下一条消息

### 队列溢出
- 队列满时丢弃新消息
- 通过调试日志记录丢弃事件

### 连接断开
- 云端连接断开时自动重置所有处理状态
- 重连后恢复正常处理

## 性能优化建议

1. **队列大小**：根据实际消息频率和MTU性能调整
2. **超时时间**：根据设备响应时间特性设置
3. **调试模式**：生产环境关闭以节省资源
4. **定时器间隔**：主处理循环100ms间隔，可根据需要调整

## 兼容性说明

- 支持汉枫全系列IOT设备（除GPRS蜂窝网产品）
- 兼容现有的道闸控制器协议
- 向下兼容：非格式化消息会直接透传

## 故障排除

### 常见问题

1. **消息处理缓慢**
   - 检查`MSG_TIMEOUT`设置是否过长
   - 确认设备响应正常

2. **队列经常满**
   - 增加`QUEUE_MAX_SIZE`
   - 检查设备处理能力

3. **调试信息不显示**
   - 确认`DEBUG_MODE = 1`
   - 检查串口连接

### 日志分析
通过串口调试信息可以分析：
- 消息处理频率
- 队列使用情况
- 超时发生频率
- 连接稳定性

## 版本历史

- V1.0 (2024-12-19): 初始版本，实现基本同步消息处理功能
