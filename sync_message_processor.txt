# 同步消息处理脚本 - 道闸控制器
# 功能：实现云端到末端设备的同步消息处理，带ID和时间戳管理
# 版本：V1.0
# 日期：2024-12-19

# ========== 配置参数 ==========
# 队列最大长度（考虑MTU性能限制）
FLASH(NUM) QUEUE_MAX_SIZE = 10
# 消息处理超时时间（毫秒）
FLASH(NUM) MSG_TIMEOUT = 5000
# 调试模式开关
FLASH(NUM) DEBUG_MODE = 1

FLASHMAGIC = 100

# ========== 全局变量 ==========
# 消息队列相关
queueSize = 0           # 当前队列长度
queueHead = 0           # 队列头指针
queueTail = 0           # 队列尾指针

# 消息处理状态
processingState = 0     # 0=空闲, 1=处理中, 2=等待响应
currentMsgId = ""       # 当前处理消息的ID
currentTimestamp = ""   # 当前处理消息的时间戳
lastSendTime = 0        # 最后发送时间
waitingForResponse = 0  # 等待响应标志

# 队列存储数组（简化实现，存储字符串格式）
queue0 = ""
queue1 = ""
queue2 = ""
queue3 = ""
queue4 = ""
queue5 = ""
queue6 = ""
queue7 = ""
queue8 = ""
queue9 = ""

# 连接状态
cloudConnected = 0      # 云端连接状态

# ========== 辅助函数 ==========
FUNCTION enqueue(message)
    result = 0
    IF (queueSize < QUEUE_MAX_SIZE)
        # 根据队列尾指针存储消息
        IF (queueTail == 0)
            queue0 = message
        END
        IF (queueTail == 1)
            queue1 = message
        END
        IF (queueTail == 2)
            queue2 = message
        END
        IF (queueTail == 3)
            queue3 = message
        END
        IF (queueTail == 4)
            queue4 = message
        END
        IF (queueTail == 5)
            queue5 = message
        END
        IF (queueTail == 6)
            queue6 = message
        END
        IF (queueTail == 7)
            queue7 = message
        END
        IF (queueTail == 8)
            queue8 = message
        END
        IF (queueTail == 9)
            queue9 = message
        END
        
        queueTail = queueTail + 1
        IF (queueTail >= QUEUE_MAX_SIZE)
            queueTail = 0
        END
        queueSize = queueSize + 1
        result = 1
    END
    RETURN(result)
END

FUNCTION dequeue()
    message = ""
    IF (queueSize > 0)
        # 根据队列头指针获取消息
        IF (queueHead == 0)
            message = queue0
            queue0 = ""
        END
        IF (queueHead == 1)
            message = queue1
            queue1 = ""
        END
        IF (queueHead == 2)
            message = queue2
            queue2 = ""
        END
        IF (queueHead == 3)
            message = queue3
            queue3 = ""
        END
        IF (queueHead == 4)
            message = queue4
            queue4 = ""
        END
        IF (queueHead == 5)
            message = queue5
            queue5 = ""
        END
        IF (queueHead == 6)
            message = queue6
            queue6 = ""
        END
        IF (queueHead == 7)
            message = queue7
            queue7 = ""
        END
        IF (queueHead == 8)
            message = queue8
            queue8 = ""
        END
        IF (queueHead == 9)
            message = queue9
            queue9 = ""
        END
        
        queueHead = queueHead + 1
        IF (queueHead >= QUEUE_MAX_SIZE)
            queueHead = 0
        END
        queueSize = queueSize - 1
    END
    RETURN(message)
END

FUNCTION extractMessageParts(fullMessage)
    # 消息格式：原始指令 + ID(4字节) + 时间戳(4字节)
    msgLen = fullMessage.length()
    IF (msgLen > 8)
        # 提取原始指令（除去最后8字节）
        originalCmd = fullMessage.subString(0, msgLen - 8)
        # 提取ID（倒数第8-5字节）
        msgId = fullMessage.subString(msgLen - 8, msgLen - 4)
        # 提取时间戳（最后4字节）
        timestamp = fullMessage.subString(msgLen - 4, msgLen)
        
        currentMsgId = msgId
        currentTimestamp = timestamp
        RETURN(originalCmd)
    END
    RETURN("")
END

FUNCTION debugLog(message)
    IF (DEBUG_MODE == 1)
        debugMsg = "[DEBUG] " + message + "\r\n"
        SEND(UART, uart0, debugMsg)
    END
END

# ========== 云端连接管理 ==========
CONN SOCK netp
    cloudConnected = 1
    debugLog("Cloud connected")
END

DISCONN SOCK netp
    cloudConnected = 0
    processingState = 0
    waitingForResponse = 0
    debugLog("Cloud disconnected")
END

# ========== 云端消息接收处理 ==========
RECV SOCK netp
    IF (cloudConnected == 1)
        # 将接收到的消息加入队列
        enqueueResult = enqueue(INPUT)
        IF (enqueueResult == 1)
            debugLog("Message enqueued, queue size: " + queueSize.prtString())
        ELSE
            debugLog("Queue full, message dropped")
        END
    END
    RETURN(FALSE)
END

# ========== 末端设备响应处理 ==========
RECV UART uart0
    IF (waitingForResponse == 1)
        # 收到末端设备响应，添加源ID和当前时间戳
        currentTime = SYSTIME
        currentTimeBytes = currentTime.toString(0, 3)
        
        # 构造响应消息：原始响应 + 源ID + 当前时间戳
        responseWithId = INPUT + currentMsgId + currentTimeBytes
        
        # 发送给云端
        IF (cloudConnected == 1)
            SEND(SOCK, netp, responseWithId)
            debugLog("Response sent to cloud with ID and timestamp")
        END
        
        # 重置处理状态
        processingState = 0
        waitingForResponse = 0
        currentMsgId = ""
        currentTimestamp = ""
    ELSE
        # 非预期的串口数据，直接透传（兼容模式）
        IF (cloudConnected == 1)
            SEND(SOCK, netp, INPUT)
            debugLog("Unexpected UART data forwarded")
        END
    END
    RETURN(FALSE)
END

# ========== 主处理循环 ==========
TIMER MessageProcessor 100
    currentTime = SYSTIME
    
    # 检查超时
    IF (waitingForResponse == 1)
        IF ((currentTime - lastSendTime) > MSG_TIMEOUT)
            debugLog("Message timeout, resetting state")
            processingState = 0
            waitingForResponse = 0
            currentMsgId = ""
            currentTimestamp = ""
        END
    END
    
    # 处理队列中的消息
    IF (processingState == 0)
        IF (queueSize > 0)
            # 从队列取出消息
            fullMessage = dequeue()
            IF (fullMessage != "")
                # 解析消息
                originalCmd = extractMessageParts(fullMessage)
                IF (originalCmd != "")
                    # 发送给末端设备
                    SEND(UART, uart0, originalCmd)
                    processingState = 1
                    waitingForResponse = 1
                    lastSendTime = currentTime
                    debugLog("Command sent to device, waiting for response")
                ELSE
                    debugLog("Invalid message format")
                END
            END
        END
    END
END

# ========== 状态监控 ==========
TIMER StatusMonitor 10000
    IF (DEBUG_MODE == 1)
        statusMsg = "Status - Queue: " + queueSize.prtString() + 
                   ", State: " + processingState.prtString() + 
                   ", Connected: " + cloudConnected.prtString() + "\r\n"
        SEND(UART, uart0, statusMsg)
    END
END
