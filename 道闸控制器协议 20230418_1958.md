﻿串口道闸控制器协议

V1.1    2023-04-18 

一，串口基本要求

起始位：1 位 数据位：8 位 奇偶校验位：0 位 停止位：1 位 波特率是 9600 

二，协议基本格式

协议格式：

起始符(0x9A) +  地址  +  命令   +  方向  +  操作   +  数据  +  校验码   +  结束符(0x9F) 

|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |n |1 |1 个字节 |
|0x9A ||||[写/读] ||CRC8 |0x9F |

地址：设备地址，范围：1-254 

命令：分为参数设置,参数读取,闸机控制等内容

方向：1--电脑到控制板     0--控制板到电脑

操作：1--设置参数         0--读取参数 

校验码为不包含起始符和结束符的全部字节 CRC8 值 

发送数据处理流程：

获取原始数据：原始数据=  地址+命令+方向+操作+字节数组+校验码 转义包装数据：检查原始数据中是否存在 0x9A-0x9F 范围的字节，将其转为 0x9C +（0x100- 原始值） 

加包头，包尾：起始符  +  转义包装数据  +  结束符 

【注】所有示例的地址为 01。 

【注】本协议支持控制多个不同地址的 485 闸机，闸机地址范围是 0x01 到 0xfe 

三，参数设置

必须是闸机在停止状态下，才能参数设置。

电脑端格式如下**:** 

|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |1 |1 |1 个字节 |
|0x9A |xx |**xx** |1 |1[写] |xx |CRC8 |0x9F |

地址：1-254 

命令：0-28 

方向：1，表示电脑端到控制板

操作：1，表示参数设置

数据：1 个字节，表示参数值

示例：9a 01 00 01 01 15 00 9f   [开闸速度设为 21]  

控制板端格式。 

|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |1 |1 |1 个字节 |
|0x9A |xx |**xx** |0 |1[写] |xx |CRC8 |0x9F |

地址，命令，操作与电脑端相同。

方向：0，表示控制板到电脑

数据：1 失败,2 成功 

示例：9a 01 00 00 01 02 b5 9f   [开闸速度]  

四，参数读取

电脑端格式如下**:** 

|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |1 |1 |1 个字节 |
|0x9A |xx |**xx** |1 |0[读] |0 |CRC8 |0x9F |

地址：1-254 

命令：0-28 

方向：1，表示电脑端到控制板

操作：0，表示参数读取

数据：1 个字节，保留

示例：9a 01 00 01 00 00 66 9f    [读取开闸速度]        控制板端格式。 



|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |4 |1 |1 个字节 |
|0x9A |xx |**xx** |0 |0[读] |xx |CRC8 |0x9F |

地址，命令，操作与电脑端相同。

方向：0，表示控制板到电脑

数据：第 1 个字节:参数值，第 2 个字节:最小值，第 3 个字节:最大值  第 4 个字节:保留 示例：9a 01 00 00 00 15 0a 2d 00 d8 9f   [开闸速度为 21,范围 10-45]  

五，闸机控制

电脑端格式如下**:** 

|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |1 |1 |1 个字节 |
|0x9A |xx |**xx** |1 |1[写] |0 |CRC8 |0x9F |

地址：1-254 

命令：101-106 

方向：1，表示电脑端到控制板 操作：1，表示闸机控制 数据：1 个字节，保留

示例：9a 01 66 01 01 00 3b 9f   [开闸] 

9a 01 67 01 01 00 b4 9f   [落闸] 

9a 01 68 01 01 00 2e 9f   [锁闸]  

9a 01 69 01 01 00 a1 9f   [解锁]  控制板端格式。 

|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |1 |1 |1 个字节 |
|0x9A |xx |**xx** |0 |1[写] |xx |CRC8 |0x9F |

地址，命令，操作与电脑端相同。方向：0，表示控制板到电脑 数据：1 失败,2 成功 

示例：9a 01 66 00 01 02 2c 9f   [开闸]  

`       `9a 01 67 00 01 02 a3 9f   [落闸]  

`       `9a 01 68 00 01 02 39 9f   [锁闸]  

9a 01 69 00 01 02 b6 9f   [解锁]                            

六，闸机信息

电脑端格式如下**:** 

|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |1 |1 |1 个字节 |
|0x9A |xx |**xx** |1 |0[读] |0 |CRC8 |0x9F |

地址：1-254 

命令：120-123 

方向：1，表示电脑端到控制板

操作：0，表示参数读取

数据：1 个字节，保留

示例：9a 01 78 01 00 00 d2 9f    [获取闸机状态]        控制板端格式。 

|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |4 |1 |1 个字节 |
|0x9A |xx |**xx** |0 |0[读] |xx |CRC8 |0x9F |

地址，命令，操作与电脑端相同。

方向：0，表示控制板到电脑

数据：信息内容

示例：9a 01 78 00 00 20 01 00 00 ba 9f   [闸机状态]           

七，闸机动态信息

【注】本命令可以 **150ms** 间隔下发。但注意控制板在保存到 **EEP** 时会影响响应。 电脑端格式：

|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |1 |1 |1 个字节 |
|0x9A |xx |**xx** |1 |0[读] |0 |CRC8 |0x9F |

地址：1-254 

命令：254 

方向：1，表示电脑端到控制板

操作：0，表示参数读取

数据：1 个字节，保留

示例：9a 01 fe 01 00 00 02 9f   [动态信息]  

控制板端格式。 

|起始符 |地址 |命令 |方向 |操作 |数据 |校验码 |结束符 |
| - | - | - | - | - | - | - | - |
|1 个字节 |1 |1 |1 |1 |6 |1 |1 个字节 |
|0x9A |xx |**xx** |0 |0[读] |xx |CRC8 |0x9F |

地址，命令，操作与电脑端相同。

方向：0，表示控制板到电脑

数据：动态信息

示例：9a 01 fe 00 00 00 00 08 00 16 17 84 9f   [动态信息]  

附录 **1**  参数列表** 



|开闸速度 ||0[0x00] |地感计数 |15[0x0f] |
| - | :- | - | - | - |
|落闸速度 ||1[0x01] |版本 |16[0x10] |
|开闸减速角度||2[0x02] |光敏阀值 |17[0x11] |
|落闸减速角度||3[0x03] |继电器输出方式|18[0x12] |
|开闸结束速度||4[0x04] |反向缓冲设置|19[0x13] |
|落闸结束速度||5[0x05] |通讯地址 |20[0x14] |
|垂直位置调节||6[0x06] |关闭地感的角度|21[0x15] |
|水平位置调节||7[0x07] |防砸是否开启|22[0x16] |
|过车延时落杆时间||8[0x08] |过流电流值|23[0x17] |
|开闸找零速度||9[0x09] |输入电源检测延时|24[0x18] |
|落闸找零速度||10[0x0a] |输入电源正常值|25[0x19] |
|遇阻反弹灵敏度||11[0x0b] |输入电源欠压值|26[0x1a] |
|遇阻力度 ||12[0x0c] |地感快速落杆开关|27[0x1b] |
|转动方向 ||13[0x0d] |参数保存  1 保存  0|28[0x1c] |
||||覆盖缓存 ||
|锁杆力度 ||14[0x0e] |||
【注】参数保存，只有写功能没有读功能。值为 1，表示将当前缓存中的参数保存到 EEP。 值为 0，表示从 EEP 中读取参数值来覆盖当前缓存中的值

附录 **2**  闸机控制列表



|停止 |101[0x65] |
| - | - |



|开闸 |102[0x66] |
| - | - |
|落闸 |103[0x67] |
|加锁 |104[0x68] |
|解锁 |105[0x69] |
|复位 |106[0x6a] |

附录 **3** 闸机信息列表



|获取闸机状态|120[0x78] |
| - | - |
|闸机信息 1 |121[0x79] |
|闸机信息 2 |122[0x7a] |
|闸机信息 3 |123[0x7b] |

附录 **4**  设备状态** 

设备状态第一字节内容 位 0--地感检测 

位 1--道闸红外 

位 2--落闸到位 

位 3--开闸到位   

位 4--电机运行状态  0--电机停止  1--电机运行 位 5--电机 HALL 出错 

位 6--过流 

位 7--电源电压异常

设备状态第二字节内容 位 0--未找零 

位 1--正在开 

位 2--正在关 

位 3--车队模式 

位 4--老化测试模式

设备状态第三字节内容

位 0-3 开闸方式: 0--无效 1--按键 2--外接端口 3--地感 4--RF 5--485 6--红外 7--其他  

位 4-7 落闸方式: 0--无效 1--按键 2--外接端口 3--地感 4--RF 5--485 6--红外 7--其他 

设备状态第四字节内容

位 0-3  停止方式: 0--无效  1--按键  2--外接端口  3--地感  4--RF 5--485 6--红外  7--其他 

附录 **5**  闸机信息 **1** 

字节 1---相对位移百分比 字节 2---速度 

字节 3---保留 

字节 4---驱动力 

附录 **6**  闸机信息 **2** 

字节 1---电流       单位是 0.1A 

字节 2---内部电压   单位是 1.0V 

字节 3---电源电压高字节  单位是 1.0mV 字节 4---电源电压低字节

附录 **7**  闸机信息 **3** 

字节 1 开闸方式: 0--无效 1--按键 2--外接端口 3--地感 4--RF 5--485 6--红外 7--其他  

字节 2 落闸方式: 0--无效 1--按键 2--外接端口 3--地感 4--RF 5--485 6--红外 7--其他  

字节 3 停止方式: 0--无效 1--按键 2--外接端口 3--地感 4--RF 5--485 6--红外 7--其他  字节 4  保留 

附录 **8**  闸机动态信息

字节 1---最高位为 0 表示电机待机 字节 2---速度 

字节 3---驱动力 

字节 4---电流       单位是 0.1A 字节 5---内部电压   单位是 1.0V 字节 6---电源电压   单位是 1.0V 

  为 1 表示电机正在运行。其余位表示相对位移百分比

附录 9 CRC 程序 

static const u8 crc\_array[] =  

{ 

0x00, 0x5e, 0xbc, 0xe2, 0x61, 0x3f, 0xdd, 0x83,  0xc2, 0x9c, 0x7e, 0x20, 0xa3, 0xfd, 0x1f, 0x41,  0x9d, 0xc3, 0x21, 0x7f, 0xfc, 0xa2, 0x40, 0x1e,  0x5f, 0x01, 0xe3, 0xbd, 0x3e, 0x60, 0x82, 0xdc,  0x23, 0x7d, 0x9f, 0xc1, 0x42, 0x1c, 0xfe, 0xa0,  0xe1, 0xbf, 0x5d, 0x03, 0x80, 0xde, 0x3c, 0x62,  0xbe, 0xe0, 0x02, 0x5c, 0xdf, 0x81, 0x63, 0x3d,  0x7c, 0x22, 0xc0, 0x9e, 0x1d, 0x43, 0xa1, 0xff,  0x46, 0x18, 0xfa, 0xa4, 0x27, 0x79, 0x9b, 0xc5,  0x84, 0xda, 0x38, 0x66, 0xe5, 0xbb, 0x59, 0x07,  

0xdb, 0x85, 0x67, 0x39, 0xba, 0xe4, 0x06, 0x58,  0x19, 0x47, 0xa5, 0xfb, 0x78, 0x26, 0xc4, 0x9a,  0x65, 0x3b, 0xd9, 0x87, 0x04, 0x5a, 0xb8, 0xe6,  0xa7, 0xf9, 0x1b, 0x45, 0xc6, 0x98, 0x7a, 0x24,  0xf8, 0xa6, 0x44, 0x1a, 0x99, 0xc7, 0x25, 0x7b,  0x3a, 0x64, 0x86, 0xd8, 0x5b, 0x05, 0xe7, 0xb9,  0x8c, 0xd2, 0x30, 0x6e, 0xed, 0xb3, 0x51, 0x0f,  0x4e, 0x10, 0xf2, 0xac, 0x2f, 0x71, 0x93, 0xcd,  0x11, 0x4f, 0xad, 0xf3, 0x70, 0x2e, 0xcc, 0x92,  0xd3, 0x8d, 0x6f, 0x31, 0xb2, 0xec, 0x0e, 0x50,  0xaf, 0xf1, 0x13, 0x4d, 0xce, 0x90, 0x72, 0x2c,  0x6d, 0x33, 0xd1, 0x8f, 0x0c, 0x52, 0xb0, 0xee,  0x32, 0x6c, 0x8e, 0xd0, 0x53, 0x0d, 0xef, 0xb1,  0xf0, 0xae, 0x4c, 0x12, 0x91, 0xcf, 0x2d, 0x73,  0xca, 0x94, 0x76, 0x28, 0xab, 0xf5, 0x17, 0x49,  0x08, 0x56, 0xb4, 0xea, 0x69, 0x37, 0xd5, 0x8b,  0x57, 0x09, 0xeb, 0xb5, 0x36, 0x68, 0x8a, 0xd4,  0x95, 0xcb, 0x29, 0x77, 0xf4, 0xaa, 0x48, 0x16,  0xe9, 0xb7, 0x55, 0x0b, 0x88, 0xd6, 0x34, 0x6a,  0x2b, 0x75, 0x97, 0xc9, 0x4a, 0x14, 0xf6, 0xa8,  0x74, 0x2a, 0xc8, 0x96, 0x15, 0x4b, 0xa9, 0xf7,  0xb6, 0xe8, 0x0a, 0x54, 0xd7, 0x89, 0x6b, 0x35,  

}; 

u8 GetCRC8(u8\* bs,int count) 

{ 

`     `u8 i,crc8 = 0; 

`     `for (i = 0; i < count; i++)     { 

`         `crc8 = crc\_array[crc8 ^ bs[i]];       } 

`     `return crc8; 

} 
