# MTU同步消息处理脚本部署检查清单

## 部署前准备

### 1. 环境检查
- [ ] 确认MTU设备型号和固件版本
- [ ] 确认支持HIS脚本功能
- [ ] 备份现有配置和脚本
- [ ] 准备IOTService工具

### 2. 网络配置检查
- [ ] MQTT服务器连接正常
- [ ] Socket配置名称为 `netp`
- [ ] 串口配置名称为 `uart0`
- [ ] 关闭原有"UART TO MQTT"透传功能
- [ ] 确认MQTT主题配置

### 3. 设备连接检查
- [ ] RS485连接到道闸控制器正常
- [ ] 道闸控制器地址配置正确
- [ ] 串口参数匹配（9600,8,N,1）
- [ ] 测试基本道闸控制功能

## 脚本部署步骤

### 1. 测试脚本部署（推荐先测试）
- [ ] 上传 `test_sync_processor.txt`
- [ ] 重启MTU设备
- [ ] 检查串口调试输出
- [ ] 验证基本功能

### 2. 生产脚本部署
- [ ] 上传 `sync_message_processor.txt`
- [ ] 重启MTU设备
- [ ] 读取脚本参数
- [ ] 配置FLASH参数

### 3. 参数配置
- [ ] 设置 `QUEUE_MAX_SIZE`（建议5-10）
- [ ] 设置 `MSG_TIMEOUT`（建议3000-5000ms）
- [ ] 设置 `DEBUG_MODE`（测试时为1，生产时为0）
- [ ] 保存参数并重启

## 功能测试

### 1. 连接测试
- [ ] 云端MQTT连接正常
- [ ] MTU设备在线状态
- [ ] 串口通信正常
- [ ] 调试信息输出正常

### 2. 消息队列测试
- [ ] 发送单条消息，验证处理
- [ ] 发送多条消息，验证队列
- [ ] 测试队列满时的处理
- [ ] 验证消息顺序处理

### 3. 同步处理测试
- [ ] 验证消息ID解析
- [ ] 验证时间戳处理
- [ ] 验证响应增强
- [ ] 测试超时处理

### 4. 道闸控制测试
- [ ] 开闸命令测试
- [ ] 落闸命令测试
- [ ] 状态查询测试
- [ ] 错误响应处理

## 性能验证

### 1. 响应时间测试
- [ ] 单条消息响应时间 < 1秒
- [ ] 队列处理延迟 < 500ms
- [ ] 超时机制正常工作
- [ ] 无消息丢失

### 2. 并发处理测试
- [ ] 快速连续发送5条消息
- [ ] 验证处理顺序正确
- [ ] 验证响应ID匹配
- [ ] 验证时间戳递增

### 3. 稳定性测试
- [ ] 连续运行1小时无异常
- [ ] 网络断开重连正常
- [ ] 设备重启恢复正常
- [ ] 内存使用稳定

## 故障排除

### 1. 常见问题检查
- [ ] 脚本语法错误
- [ ] 参数配置错误
- [ ] 网络连接问题
- [ ] 串口通信问题

### 2. 调试信息分析
- [ ] 连接状态日志
- [ ] 消息队列状态
- [ ] 处理状态变化
- [ ] 错误和异常日志

### 3. 性能问题排查
- [ ] 队列溢出频率
- [ ] 超时发生频率
- [ ] 消息处理延迟
- [ ] 设备响应时间

## 生产环境配置

### 1. 优化配置
- [ ] 关闭调试模式（DEBUG_MODE = 0）
- [ ] 优化队列大小
- [ ] 调整超时时间
- [ ] 优化定时器间隔

### 2. 监控配置
- [ ] 配置状态监控
- [ ] 设置告警阈值
- [ ] 配置日志记录
- [ ] 建立维护计划

### 3. 备份和恢复
- [ ] 备份工作配置
- [ ] 准备回滚方案
- [ ] 文档化配置参数
- [ ] 建立维护手册

## 验收标准

### 1. 功能要求
- [ ] 消息顺序处理正确
- [ ] ID和时间戳处理正确
- [ ] 响应增强功能正常
- [ ] 超时保护机制有效

### 2. 性能要求
- [ ] 消息处理延迟 < 1秒
- [ ] 队列处理能力 ≥ 10条/分钟
- [ ] 系统稳定运行 ≥ 24小时
- [ ] 内存使用 < 80%

### 3. 可靠性要求
- [ ] 网络断开自动恢复
- [ ] 设备重启自动恢复
- [ ] 异常情况自动处理
- [ ] 无消息丢失或重复

## 部署完成确认

### 1. 功能确认
- [ ] 所有测试用例通过
- [ ] 性能指标达标
- [ ] 稳定性验证通过
- [ ] 用户验收通过

### 2. 文档确认
- [ ] 部署文档完整
- [ ] 配置参数记录
- [ ] 维护手册准备
- [ ] 培训材料准备

### 3. 交付确认
- [ ] 系统正常运行
- [ ] 监控配置完成
- [ ] 维护计划制定
- [ ] 技术支持安排

## 签字确认

| 角色 | 姓名 | 签字 | 日期 |
|------|------|------|------|
| 开发人员 |  |  |  |
| 测试人员 |  |  |  |
| 运维人员 |  |  |  |
| 项目经理 |  |  |  |

---

**注意事项：**
1. 请严格按照检查清单执行每个步骤
2. 遇到问题及时记录和处理
3. 保持与相关人员的沟通
4. 确保所有文档和配置的完整性
