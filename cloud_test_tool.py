#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端测试工具 - 用于测试MTU同步消息处理脚本
功能：发送带ID和时间戳的道闸控制指令，接收并验证响应
"""

import paho.mqtt.client as mqtt
import time
import struct
import json
from datetime import datetime

class CloudTestTool:
    def __init__(self, mqtt_broker, mqtt_port, topic_pub, topic_sub):
        self.broker = mqtt_broker
        self.port = mqtt_port
        self.topic_pub = topic_pub  # 发布到MTU的主题
        self.topic_sub = topic_sub  # 订阅MTU响应的主题
        self.client = mqtt.Client()
        self.message_id = 1
        self.sent_messages = {}  # 存储已发送的消息
        
        # 道闸控制指令定义
        self.gate_commands = {
            'open': bytes([0x9a, 0x01, 0x66, 0x01, 0x01, 0x00, 0x3b, 0x9f]),    # 开闸
            'close': bytes([0x9a, 0x01, 0x67, 0x01, 0x01, 0x00, 0xb4, 0x9f]),   # 落闸
            'lock': bytes([0x9a, 0x01, 0x68, 0x01, 0x01, 0x00, 0x2e, 0x9f]),    # 锁闸
            'unlock': bytes([0x9a, 0x01, 0x69, 0x01, 0x01, 0x00, 0xa1, 0x9f]),  # 解锁
            'status': bytes([0x9a, 0x01, 0x78, 0x01, 0x00, 0x00, 0xd2, 0x9f]),  # 获取状态
        }
        
        self.setup_mqtt()
    
    def setup_mqtt(self):
        """设置MQTT连接"""
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect
        
    def on_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            print(f"✓ 已连接到MQTT服务器 {self.broker}:{self.port}")
            client.subscribe(self.topic_sub)
            print(f"✓ 已订阅响应主题: {self.topic_sub}")
        else:
            print(f"✗ MQTT连接失败，错误代码: {rc}")
    
    def on_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        print(f"✗ MQTT连接断开，错误代码: {rc}")
    
    def on_message(self, client, userdata, msg):
        """接收MTU响应消息"""
        try:
            response_data = msg.payload
            print(f"\n📨 收到响应 (长度: {len(response_data)})")
            print(f"   原始数据: {response_data.hex()}")
            
            # 解析响应消息
            if len(response_data) >= 8:
                # 提取源ID和时间戳（最后8字节）
                device_response = response_data[:-8]
                source_id = response_data[-8:-4]
                timestamp = response_data[-4:]
                
                print(f"   设备响应: {device_response.hex()}")
                print(f"   源消息ID: {source_id.hex()}")
                print(f"   时间戳: {struct.unpack('<I', timestamp)[0]}")
                
                # 验证响应
                self.verify_response(source_id, device_response)
            else:
                print(f"   响应格式异常，长度不足")
                
        except Exception as e:
            print(f"✗ 解析响应失败: {e}")
    
    def verify_response(self, source_id, device_response):
        """验证响应消息"""
        msg_id = struct.unpack('<I', source_id)[0]
        if msg_id in self.sent_messages:
            sent_info = self.sent_messages[msg_id]
            print(f"✓ 响应匹配，命令: {sent_info['command']}")
            print(f"   发送时间: {sent_info['send_time']}")
            print(f"   响应时间: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
            
            # 分析设备响应
            self.analyze_device_response(sent_info['command'], device_response)
            
            # 清理已处理的消息
            del self.sent_messages[msg_id]
        else:
            print(f"⚠ 未找到对应的发送记录，ID: {msg_id}")
    
    def analyze_device_response(self, command, response):
        """分析设备响应内容"""
        if len(response) >= 8:
            # 道闸协议响应格式分析
            if response[0] == 0x9a and response[-1] == 0x9f:
                addr = response[1]
                cmd = response[2]
                direction = response[3]
                operation = response[4]
                data = response[5]
                
                print(f"   地址: {addr}, 命令: 0x{cmd:02x}, 方向: {direction}, 操作: {operation}")
                
                if data == 2:
                    print(f"   ✓ {command} 命令执行成功")
                elif data == 1:
                    print(f"   ✗ {command} 命令执行失败")
                else:
                    print(f"   ? {command} 命令状态未知: {data}")
            else:
                print(f"   响应格式不符合道闸协议")
    
    def create_message_with_id_timestamp(self, command_bytes, msg_id=None):
        """创建带ID和时间戳的消息"""
        if msg_id is None:
            msg_id = self.message_id
            self.message_id += 1
        
        # 生成时间戳（当前时间的秒数）
        timestamp = int(time.time())
        
        # 打包ID和时间戳（小端序）
        id_bytes = struct.pack('<I', msg_id)
        timestamp_bytes = struct.pack('<I', timestamp)
        
        # 组合消息：原始指令 + ID + 时间戳
        full_message = command_bytes + id_bytes + timestamp_bytes
        
        # 记录发送信息
        self.sent_messages[msg_id] = {
            'command': self.get_command_name(command_bytes),
            'send_time': datetime.now().strftime('%H:%M:%S.%f')[:-3],
            'timestamp': timestamp
        }
        
        return full_message, msg_id
    
    def get_command_name(self, command_bytes):
        """根据指令字节获取命令名称"""
        for name, cmd_bytes in self.gate_commands.items():
            if cmd_bytes == command_bytes:
                return name
        return "unknown"
    
    def send_command(self, command_name):
        """发送指定命令"""
        if command_name not in self.gate_commands:
            print(f"✗ 未知命令: {command_name}")
            return False
        
        command_bytes = self.gate_commands[command_name]
        message, msg_id = self.create_message_with_id_timestamp(command_bytes)
        
        try:
            result = self.client.publish(self.topic_pub, message)
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                print(f"📤 发送命令: {command_name} (ID: {msg_id})")
                print(f"   消息长度: {len(message)} 字节")
                print(f"   完整消息: {message.hex()}")
                return True
            else:
                print(f"✗ 发送失败，错误代码: {result.rc}")
                return False
        except Exception as e:
            print(f"✗ 发送异常: {e}")
            return False
    
    def connect(self):
        """连接到MQTT服务器"""
        try:
            self.client.connect(self.broker, self.port, 60)
            self.client.loop_start()
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开MQTT连接"""
        self.client.loop_stop()
        self.client.disconnect()
    
    def run_test_sequence(self):
        """运行测试序列"""
        print("\n🚀 开始测试序列...")
        
        test_commands = ['status', 'open', 'status', 'close', 'status']
        
        for i, cmd in enumerate(test_commands):
            print(f"\n--- 测试 {i+1}/{len(test_commands)} ---")
            if self.send_command(cmd):
                time.sleep(3)  # 等待响应
            else:
                print(f"跳过命令: {cmd}")
        
        print(f"\n✓ 测试序列完成")
        
        # 等待剩余响应
        print("等待剩余响应...")
        time.sleep(5)
        
        # 检查未响应的消息
        if self.sent_messages:
            print(f"⚠ 有 {len(self.sent_messages)} 条消息未收到响应:")
            for msg_id, info in self.sent_messages.items():
                print(f"   ID {msg_id}: {info['command']} ({info['send_time']})")

def main():
    """主函数"""
    print("=== MTU同步消息处理测试工具 ===")
    
    # 配置MQTT参数（请根据实际情况修改）
    MQTT_BROKER = "localhost"  # MQTT服务器地址
    MQTT_PORT = 1883           # MQTT端口
    TOPIC_PUB = "mtu/command"  # 发送命令的主题
    TOPIC_SUB = "mtu/response" # 接收响应的主题
    
    # 创建测试工具
    test_tool = CloudTestTool(MQTT_BROKER, MQTT_PORT, TOPIC_PUB, TOPIC_SUB)
    
    # 连接MQTT服务器
    if not test_tool.connect():
        return
    
    try:
        # 等待连接稳定
        time.sleep(2)
        
        # 交互式测试
        while True:
            print("\n=== 测试选项 ===")
            print("1. 开闸 (open)")
            print("2. 落闸 (close)")
            print("3. 锁闸 (lock)")
            print("4. 解锁 (unlock)")
            print("5. 查询状态 (status)")
            print("6. 运行测试序列")
            print("0. 退出")
            
            choice = input("请选择操作 (0-6): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                test_tool.send_command('open')
            elif choice == '2':
                test_tool.send_command('close')
            elif choice == '3':
                test_tool.send_command('lock')
            elif choice == '4':
                test_tool.send_command('unlock')
            elif choice == '5':
                test_tool.send_command('status')
            elif choice == '6':
                test_tool.run_test_sequence()
            else:
                print("无效选择")
            
            time.sleep(1)
    
    except KeyboardInterrupt:
        print("\n用户中断")
    
    finally:
        test_tool.disconnect()
        print("测试结束")

if __name__ == "__main__":
    main()
