# 同步消息处理脚本测试版本
# 用于验证功能的简化测试脚本
# 版本：V1.0-TEST

# ========== 测试配置 ==========
QUEUE_MAX_SIZE = 5      # 测试用较小队列
MSG_TIMEOUT = 3000      # 3秒超时
DEBUG_MODE = 1          # 开启调试

# ========== 全局变量 ==========
queueSize = 0
queueHead = 0
queueTail = 0
processingState = 0
currentMsgId = ""
waitingForResponse = 0
lastSendTime = 0
cloudConnected = 0

# 简化队列存储
queue0 = ""
queue1 = ""
queue2 = ""
queue3 = ""
queue4 = ""

# 测试计数器
testMsgCount = 0
testRespCount = 0

# ========== 简化队列函数 ==========
FUNCTION enqueue(message)
    result = 0
    IF (queueSize < QUEUE_MAX_SIZE)
        IF (queueTail == 0)
            queue0 = message
        END
        IF (queueTail == 1)
            queue1 = message
        END
        IF (queueTail == 2)
            queue2 = message
        END
        IF (queueTail == 3)
            queue3 = message
        END
        IF (queueTail == 4)
            queue4 = message
        END
        
        queueTail = queueTail + 1
        IF (queueTail >= QUEUE_MAX_SIZE)
            queueTail = 0
        END
        queueSize = queueSize + 1
        result = 1
    END
    RETURN(result)
END

FUNCTION dequeue()
    message = ""
    IF (queueSize > 0)
        IF (queueHead == 0)
            message = queue0
            queue0 = ""
        END
        IF (queueHead == 1)
            message = queue1
            queue1 = ""
        END
        IF (queueHead == 2)
            message = queue2
            queue2 = ""
        END
        IF (queueHead == 3)
            message = queue3
            queue3 = ""
        END
        IF (queueHead == 4)
            message = queue4
            queue4 = ""
        END
        
        queueHead = queueHead + 1
        IF (queueHead >= QUEUE_MAX_SIZE)
            queueHead = 0
        END
        queueSize = queueSize - 1
    END
    RETURN(message)
END

FUNCTION extractCmd(fullMessage)
    msgLen = fullMessage.length()
    IF (msgLen > 8)
        originalCmd = fullMessage.subString(0, msgLen - 8)
        currentMsgId = fullMessage.subString(msgLen - 8, msgLen - 4)
        RETURN(originalCmd)
    END
    RETURN("")
END

FUNCTION testLog(message)
    logMsg = "[TEST] " + message + "\r\n"
    SEND(UART, uart0, logMsg)
END

# ========== 连接管理 ==========
CONN SOCK netp
    cloudConnected = 1
    testLog("Cloud connected - Test started")
END

DISCONN SOCK netp
    cloudConnected = 0
    processingState = 0
    waitingForResponse = 0
    testLog("Cloud disconnected")
END

# ========== 消息接收 ==========
RECV SOCK netp
    IF (cloudConnected == 1)
        testMsgCount = testMsgCount + 1
        enqueueResult = enqueue(INPUT)
        IF (enqueueResult == 1)
            testLog("MSG#" + testMsgCount.prtString() + " queued, size=" + queueSize.prtString())
        ELSE
            testLog("MSG#" + testMsgCount.prtString() + " DROPPED - queue full")
        END
    END
    RETURN(FALSE)
END

# ========== 设备响应 ==========
RECV UART uart0
    IF (waitingForResponse == 1)
        testRespCount = testRespCount + 1
        currentTime = SYSTIME
        currentTimeBytes = currentTime.toString(0, 3)
        
        # 构造测试响应
        responseWithId = INPUT + currentMsgId + currentTimeBytes
        
        IF (cloudConnected == 1)
            SEND(SOCK, netp, responseWithId)
            testLog("RESP#" + testRespCount.prtString() + " sent with ID+timestamp")
        END
        
        processingState = 0
        waitingForResponse = 0
        currentMsgId = ""
    ELSE
        testLog("Unexpected UART data: forwarded")
        IF (cloudConnected == 1)
            SEND(SOCK, netp, INPUT)
        END
    END
    RETURN(FALSE)
END

# ========== 主处理循环 ==========
TIMER MessageProcessor 200
    currentTime = SYSTIME
    
    # 超时检查
    IF (waitingForResponse == 1)
        IF ((currentTime - lastSendTime) > MSG_TIMEOUT)
            testLog("TIMEOUT - resetting state")
            processingState = 0
            waitingForResponse = 0
            currentMsgId = ""
        END
    END
    
    # 处理队列
    IF (processingState == 0)
        IF (queueSize > 0)
            fullMessage = dequeue()
            IF (fullMessage != "")
                originalCmd = extractCmd(fullMessage)
                IF (originalCmd != "")
                    SEND(UART, uart0, originalCmd)
                    processingState = 1
                    waitingForResponse = 1
                    lastSendTime = currentTime
                    testLog("CMD sent to device, waiting response")
                ELSE
                    testLog("Invalid message format")
                END
            END
        END
    END
END

# ========== 测试状态报告 ==========
TIMER TestReport 5000
    statusMsg = "=== TEST STATUS ===\r\n" +
               "Queue: " + queueSize.prtString() + "/" + QUEUE_MAX_SIZE.prtString() + "\r\n" +
               "State: " + processingState.prtString() + " (0=idle,1=processing)\r\n" +
               "Waiting: " + waitingForResponse.prtString() + "\r\n" +
               "Connected: " + cloudConnected.prtString() + "\r\n" +
               "Messages: " + testMsgCount.prtString() + "\r\n" +
               "Responses: " + testRespCount.prtString() + "\r\n" +
               "==================\r\n"
    SEND(UART, uart0, statusMsg)
END

# ========== 测试数据生成器（可选） ==========
# 注释掉此部分用于实际测试，取消注释用于自动测试
#TIMER TestDataGen 8000
#    IF (cloudConnected == 1)
#        # 生成测试消息：道闸开闸指令 + 测试ID + 时间戳
#        testCmd = [0x9a, 0x01, 0x66, 0x01, 0x01, 0x00, 0x3b, 0x9f]
#        testId = [0x12, 0x34, 0x56, 0x78]
#        testTs = [0x00, 0x00, 0x00, 0x01]
#        testMsg = testCmd + testId + testTs
#        
#        # 模拟云端发送
#        testMsgCount = testMsgCount + 1
#        enqueueResult = enqueue(testMsg)
#        IF (enqueueResult == 1)
#            testLog("AUTO-TEST MSG#" + testMsgCount.prtString() + " generated")
#        END
#    END
#END
